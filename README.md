# DSTweaks — WordPress utility plugin

DSTweaks is a small WordPress plugin that packages a set of site-specific tweaks and Divi-compatible dynamic modules used on the author's WordPress site.

## Key points
- **Purpose**: provide reusable site tweaks, Divi modules, and small utilities for the site.
- **Structure**: plugin entry `DSTweaks.php`, modules under `modules/`, common helpers under `common/`, and language files under `languages/`.
- **Private repository recommended**: this plugin contains site-specific code — keep the repository private if you don't want it public.

## Installation (developer/local)
1. Copy the `DSTweaks` folder into `wp-content/plugins/` on your local or staging WordPress install.
2. Activate the plugin in the WordPress Admin > Plugins screen.

## Development notes
- **Code layout**:
  - `DSTweaks.php` — plugin bootstrap and hooks.
  - `modules/` — modular features (Divi modules, shortcodes, etc.).
  - `common/` — admin helpers and shared code.
  - `languages/` — translation `.po`/`.mo` files.
- **Standards**:
  - Keep PHP files compatible with your target PHP version used on your host.
  - Avoid committing credentials. If you must use secrets locally, place them in an ignored `.env` and load them at runtime.

## Git workflow (private repo)
1. Initialize and commit locally (PowerShell example):

   ```powershell
   cd 'c:/wamp64/www/mvmdashti.ir/wp-content/plugins/DSTweaks'
   if (-not (Test-Path -Path .git)) { git init }
   git add .
   git commit -m "Initial commit: DSTweaks plugin"
   ```

2. Create a private repository on your hosting provider (GitHub/GitLab/Bitbucket) and push:

   ```powershell
   # replace <URL> with the HTTPS or SSH URL for the private repo
   git remote add origin <URL>
   git branch -M main
   git push -u origin main
   ```

## Security checklist before pushing
- Remove any hard-coded passwords, API keys, or database credentials.
- Ensure `wp-config.php` and any local `.env` are listed in `.gitignore`.
- Use Personal Access Tokens (PAT) or SSH keys for remote auth; don't place them in the repo.

## Testing and debugging
- Use `WP_DEBUG` in your local `wp-config.php` to surface PHP warnings during development.
- Review `languages/` files when changing user-visible strings.

## Support & contact
- This README documents the plugin layout. For questions about this repository contact the code owner directly.

## License
- Add a LICENSE file if you want to publish with an explicit license. If private, you can skip this.

## Changelog
- 1.0 — initial local codebase
