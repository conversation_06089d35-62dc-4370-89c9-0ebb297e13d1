<?php
/**
 * Module Name: DSTweaks MVMDashti Module
 * Description: A module for deploying specific DSTweaks for MVMDashti website.
 * Version: 1.2.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_MVMDashti_Module {
    public function __construct() {
        if (!is_admin()) {
            add_action('wp_enqueue_scripts', array($this, 'dstweaks_enqueue_assets'), 999);
        }
    }

    /**
     * Enqueue assets for MVMDashti module
     *
     * @return void
     */
    public function dstweaks_enqueue_assets() {
        // Register and enqueue inline style
        wp_register_style('dstweaks-mvmdashti-style', false);
        wp_enqueue_style('dstweaks-mvmdashti-style');

        // Register and enqueue inline script (load after jQuery and Divi scripts)
        wp_register_script('dstweaks-mvmdashti-script', false, array('jquery', 'et-builder-modules-script'), null, true);
        wp_enqueue_script('dstweaks-mvmdashti-script');

/* === CSS CODE STARTS HERE (Put the code between "<<<CSS" and "CSS") === */
wp_add_inline_style('dstweaks-mvmdashti-style', <<<CSS
    /* Link disabler */
    .no-link > a {
        pointer-events: none;
        cursor: default;
    }
    /* Header Menu Fix */
    .pa-split-menu .et_pb_menu__menu,
    .pa-split-menu .et_pb_menu__menu > nav,
    .pa-split-menu .et_pb_menu__menu > nav > ul {
        width: 100%;
    }
    .pa-split-menu .et_pb_menu__menu > nav > ul li:nth-last-child(3) {
        margin-left: auto;
        margin-right: initial;
    }
    .pa-split-menu .et_pb_menu__wrap,
    .pa-split-menu .et_pb_menu__wrap .et-menu_nav {
        flex-wrap: nowrap !important;
    }
    /* Search Input Fix */
    input.et_pb_menu__search-input {
        margin-left: 130px;
        margin-right: initial;
        margin-top: 7px;
        background: rgba(255, 255, 255, 0.3) !important;
        color: white !important;
        text-align: right;
    }
CSS
);
/* === END OF CSS CODE === */

/* === JS CODE STARTS HERE (Put the code between "<<<JS" and "JS") === */
wp_add_inline_script('dstweaks-mvmdashti-script', <<<'JS'

JS
);
/* === END OF JS CODE === */
    }
}

if (class_exists('DSTweaks_MVMDashti_Module') && !isset($GLOBALS['dstweaks_modules']['mvmDashti']['obj'])) {
    $GLOBALS['dstweaks_modules']['mvmDashti']['obj'] = new DSTweaks_MVMDashti_Module();
} else
    $GLOBALS['dstweaks_modules']['mvmDashti']['errors'] = "Instantiation failed. Couldn\'t find the required class.";