<?php
/**
 * Module Name: Dynamic Slider
 * Description: A module for showing a slider using custom fields as image sources and adding a new Card Carousel appearance.
 * Version: 4.0.1
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Divi_Module_DynamicSlider extends ET_Builder_Module {
	private $slider_object;

	public function init() {
		// Create internal slider object
		$this->slider_object = new ET_Builder_Module_Slider();

		$this->name            = esc_html__( 'DST Dynamic Slider', 'et_builder' );
		$this->plural          = esc_html__( 'DST Dynamic Sliders', 'et_builder' );
		$this->slug            = 'dst_dynamic_slider';
		$this->vb_support      = 'on';
		
        // Add new toggle to internal slider config
		$this->slider_object->settings_modal_toggles['advanced']['toggles']['dst_slider_appearance_toggle'] =
			esc_html__( 'Appearance', 'dstweaks' );
	}

	public function get_fields() {
		// Use original slider fields and triger it's function
		$fields = $this->slider_object->get_fields();

		// Add custom appearance option
		$fields['dst_slider_appearance_option'] = array(
			'label'           => esc_html__( 'Appearance', 'dstweaks' ),
			'description'     => esc_html__( 'Choose the appearance style of the slider.', 'dstweaks' ),
			'type'            => 'select',
			'option_category' => 'layout',
			'default'         => 'dst_slider_standard',
			'options'         => array(
				'dst_slider_standard'      => esc_html__( 'Standard', 'dstweaks' ),
				'dst_slider_card_carousel' => esc_html__( 'Card Carousel', 'dstweaks' ),
			),
			'tab_slug'        => 'advanced',
			'toggle_slug'     => 'dst_slider_appearance_toggle',
			'mobile_options'  => true,
		);

		return $fields;
	}

	/*public function before_render() {
		// Set appearance globally (if needed elsewhere)
		global $et_pb_slider;
		$et_pb_slider['dst_slider_appearance_option'] = $this->props['dst_slider_appearance_option'];
	}*/

    public function render( $attrs, $content, $render_slug ) {
        // Calculate appearance class
        $appearance = $this->props['dst_slider_appearance_option'] ?? 'dst_slider_standard';
        $appearance_class = ( $appearance === 'dst_slider_card_carousel' )
        ? 'dst-slider-card-carousel'
        : 'dst-slider-standard';
        
        // Inject appearance class
        $this->slider_object->add_classname( $appearance_class );

        return $this->slider_object->render( $attrs, $content, $render_slug );
    }
}