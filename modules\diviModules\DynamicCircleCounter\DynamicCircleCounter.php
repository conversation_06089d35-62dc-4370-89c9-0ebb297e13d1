<?php
/**
 * Module Name: Dynamic Circle Counter
 * Description: Adds Dynamic Content support to the Circle Counter module's Number field in Divi.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Divi_Module_DynamicCircleCounter extends ET_Builder_Module_Circle_Counter {
    public $vb_support = 'on';

    function init() {
        parent::init();
        $this->name   = esc_html__( 'DST Dynamic Circle Counter', 'dstweaks' );
        $this->plural = esc_html__( 'DST Dynamic Circle Counters', 'dstweaks' );
    }

    function get_fields() {
        $fields = parent::get_fields();
        if ( isset($fields['number']) ) {
            $fields['number']['dynamic_content'] = true;
        }
        return $fields;
    }
}
