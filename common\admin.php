<?php
/**
 * DSTweaks Admin Page and related logic
 *
 * This file contains all admin page UI, settings, and related hooks.
 *
 * @package DSTweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

/**
 * Add DSTweaks settings page to WordPress admin menu
 *
 * @return void
 */
function dstweaks_add_settings_page() {
    add_options_page(
        'DSTweaks Settings',
        'DSTweaks',
        'manage_options',
        'dstweaks',
        'dstweaks_settings_page_content'
    );
}
add_action('admin_menu', 'dstweaks_add_settings_page');

/**
 * Enqueue Site Health styles for accordion UI on DSTweaks settings page
 *
 * @param string $hook The current admin page hook
 * @return void
 */
function dstweaks_admin_enqueue_site_health_assets($hook) {
    if ($hook !== 'settings_page_dstweaks') return;
    // Only enqueue Site Health CSS, not JS (we use our own JS)
    wp_enqueue_style('site-health', admin_url('css/site-health.css'), array('wp-admin'), null);
}
add_action('admin_enqueue_scripts', 'dstweaks_admin_enqueue_site_health_assets');

/**
 * Render the DSTweaks settings page content
 *
 * Displays module status, enable/disable controls, and admin interface
 *
 * @return void
 */
function dstweaks_settings_page_content() {
    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }

    $dstweaks_plugin_info = get_plugin_data(DSTWEAKS_MAIN_FILE);
    $is_user_admin        = current_user_can('administrator');
    $is_rtl               = is_rtl();

    // Per-module enable/disable logic
    $modules = isset($GLOBALS['dstweaks_modules']) ? $GLOBALS['dstweaks_modules'] : array();
    $states = get_option('dstweaks_module_states', array());
    if (isset($_POST['dstweaks_save_modules'])) {
        // Verify nonce and user capabilities
        if (!check_admin_referer('dstweaks_save_modules_action', 'dstweaks_save_modules_nonce') || 
            !current_user_can('manage_options')) {
            wp_die(__('Security check failed.', 'dstweaks'));
        }

        // Add loading state
        echo '<div id="dstweaks-loading" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(255,255,255,0.8);z-index:9999;display:flex;justify-content:center;align-items:center;">';
        echo '<div class="spinner" style="float:none;width:40px;height:40px;opacity:1"></div>';
        echo '</div>';

        $new_states = array();
        foreach ($modules as $module => $data) {
            if ($module === 'core') continue;
            // Sanitize the module state
            $new_states[$module] = isset($_POST['dstweaks_module'][$module]) ? '1' : '0';
        }
        
        // Use autoload false for better performance
        update_option('dstweaks_module_states', $new_states, false);
        $states = $new_states;
        ?>
        <script>
            window.location.href = window.location.href + '&settings-updated=true';
        </script>
        <?php
        exit;
    }

    // Handle cache clearing action
    if (isset($_POST['dstweaks_clear_cache'])) {
        check_admin_referer('dstweaks_clear_cache', 'dstweaks_clear_cache_nonce');
        
        if (function_exists('dstweaks_clear_all_caches')) {
            $cleared = dstweaks_clear_all_caches();
            if ($cleared) {
                add_settings_error(
                    'dstweaks_messages',
                    'dstweaks_cache_cleared',
                    __('All caches have been cleared successfully.', 'dstweaks'),
                    'success'
                );
            } else {
                add_settings_error(
                    'dstweaks_messages',
                    'dstweaks_cache_error',
                    __('There was an error clearing the caches.', 'dstweaks'),
                    'error'
                );
            }
        }
    }

    // Display notices
    settings_errors('dstweaks_messages');

    if (isset($_GET['settings-updated'])) {
        echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('Module settings saved.', 'dstweaks') . '</p></div>';
        // Add WordPress core notice dismissal script
        wp_enqueue_script('common');
    }

    $total_modules = count($GLOBALS['dstweaks_modules']);
    $modules_with_errors = count(array_filter($GLOBALS['dstweaks_modules'], function ($m) {return !empty($m['errors']);}));
    $modules_with_errors_percentage = 100 * (1 - ($modules_with_errors / ($total_modules + 1)));

    // Ensure label variable is defined to avoid undefined variable notices when there are no errors.
    $modules_with_errors_label = '';

    if ($modules_with_errors == 1) {
        $modules_with_errors_label = sprintf(
            /* translators: 1: plugin version */
            __('Plugin core(v%1$s) is active but 1 module is having problems.', 'dstweaks'),
            $dstweaks_plugin_info['Version']
        );
    } elseif ($modules_with_errors > 1) {
        $modules_with_errors_label = sprintf(
            /* translators: 1: plugin version, 2: number of modules with problems */
            __('Plugin core(v%1$s) is active but %2$s modules are having problems.', 'dstweaks'),
            $dstweaks_plugin_info['Version'],
            $modules_with_errors
        );
    }

    if (isset($GLOBALS['dstweaks_modules']['core'])) {
        $current_core_status = 'inactive';
        if ($is_user_admin) {
            switch ($GLOBALS['dstweaks_modules']['core']) {
                case 'manifest_missing':
                    $inactive_label = sprintf(
                        /* translators: 1: plugin version */
                        __('Module core v%1$s is inactive: Manifest file is missing.', 'dstweaks'),
                        $dstweaks_plugin_info['Version']
                    );
                    break;
                case 'manifest_unreadable':
                    $inactive_label = sprintf(
                        /* translators: 1: plugin version */
                        __('Module core v%1$s is inactive: Unable to read manifest file.', 'dstweaks'),
                        $dstweaks_plugin_info['Version']
                    );
                    break;
                case 'manifest_invalid':
                    $inactive_label = sprintf(
                        /* translators: 1: plugin version */
                        __('Module core v%1$s is inactive: Invalid manifest format.', 'dstweaks'),
                        $dstweaks_plugin_info['Version']
                    );
                    break;
            }
        } else {
            $inactive_label = sprintf(
                /* translators: 1: plugin version */
                __('Module core v%1$s is inactive.', 'dstweaks'),
                $dstweaks_plugin_info['Version']
            );
        }
    } else {
        if ($modules_with_errors > 0) {
            $current_core_status = 'errors';
        } else {
            $current_core_status = 'ok';
        }
    }

    $core_statuses = [
        'inactive' => [
            'class' => 'red',
            'label' => $inactive_label,
            'percent' => 30,
        ],
        'errors' => [
            'class' => 'orange',
            'label' => $modules_with_errors_label,
            'percent' => $modules_with_errors_percentage,
        ],
        'ok' => [
            'class' => 'green',
            'label' => sprintf(
                /* translators: 1: plugin version */
                __('Module core v%1$s is active. Everything looks fine.', 'dstweaks'),
                $dstweaks_plugin_info['Version']
            ),
            'percent' => 100,
        ],
    ];

    $core_status = $core_statuses[$current_core_status] ?? $core_statuses['inactive'];
    $offset = round(565.48 * (1 - $core_status['percent'] / 100), 2);
    ?>
    <style>
        #wpcontent {padding-left: 0 !important;}
        .health-check-header {padding-top: 30px !important;}
        .health-check-accordion {border: 1px solid #c3c4c7;margin-top: -1px;}
        #wpfooter {position: relative !important;}
        .health-check-body {margin-bottom: 65px;}
        html.wp-toolbar {padding-top: 32px !important;}
    </style>
    <div class="health-check-header">
        <div class="health-check-title-section">
            <h1><?php echo wp_kses_post(get_admin_page_title()); ?></h1>
        </div>
        <div class="health-check-title-section site-health-progress-wrapper <?php echo esc_attr($core_status['class']); ?>">
            <div class="site-health-progress">
                <svg aria-hidden="true" focusable="false" width="100%" height="100%" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg">
                    <circle r="90" cx="100" cy="100" fill="transparent" stroke-dasharray="565.48" stroke-dashoffset="0"></circle>
                    <circle id="bar" r="90" cx="100" cy="100" fill="transparent" stroke-dasharray="565.48" style="stroke-dashoffset: <?php echo esc_attr($offset); ?>;"></circle>
                </svg>
            </div>
            <div class="site-health-progress-label"><?php echo esc_html($core_status['label']); ?></div>
        </div>
    </div>
    <hr class="wp-header-end">
    <form method="post" style="width:100%;">
        <?php wp_nonce_field('dstweaks_save_modules_action', 'dstweaks_save_modules_nonce'); ?>
        <div class="health-check-body health-check-status-tab hide-if-no-js wrap<?php echo $is_rtl ? ' rtl' : ''; ?>" style="display: grid; place-items: center;">
            <div class="site-status-has-issues">
                <p><?php echo wp_kses_post($dstweaks_plugin_info['Description']); ?></p>
                <h3><?php 
                    if ($total_modules - $modules_with_errors == 0) {
                        echo wp_kses_post(sprintf(
                            /* translators: 1: total number of modules */
                            __('All %1$s modules are inactive:', 'dstweaks'),
                            $total_modules
                        ));
                    } else {
                        echo wp_kses_post(sprintf(
                            /* translators: 1: number of active modules, 2: total number of modules */
                            __('%1$s out of %2$s modules are working fine:', 'dstweaks'),
                            $total_modules - $modules_with_errors,
                            $total_modules
                        ));
                    }
                ?></h3>
                <div style="margin-bottom:16px;">
                    <button type="submit" name="dstweaks_save_modules" class="button button-primary"><?php esc_html_e('Save', 'dstweaks'); ?></button>
                    <button type="button" id="dstweaks-enable-all" class="button"><?php esc_html_e('Enable All', 'dstweaks'); ?></button>
                    <button type="button" id="dstweaks-disable-all" class="button"><?php esc_html_e('Disable All', 'dstweaks'); ?></button>
                </div>
                <?php
                $i = 0;
                foreach ($GLOBALS['dstweaks_modules'] as $module => $module_data) {
                    if ($module == 'core') continue;
                    $is_active = $module_data['status'] === 'active' || $module_data['status'] === 'internal_error';
                    $status = $module_data['status'] === 'active' ? __('Active', 'dstweaks') : ($module_data['status'] === 'internal_error' ? __('Active (internal errors)', 'dstweaks') : __('Inactive', 'dstweaks'));
                    $is_open = !empty($module_data['errors']) || (!empty($module_data['messages']) && $modules_with_errors == 0);
                    $accordion_id = 'dstweaks-accordion-' . esc_attr($module);
                    $checked = (!isset($states[$module]) || $states[$module] === '1') ? 'checked' : '';
                    $i = $i + 1;
                ?>
                <div class="health-check-accordion">
                    <button type="button" class="health-check-accordion-trigger" aria-expanded="<?php echo $is_open ? 'true' : 'false'; ?>" aria-controls="<?php echo $accordion_id; ?>">
                        <span class="title"><?php echo esc_html($i) . '- ' . wp_kses_post($module_data['name']); ?></span>
                        <span class="badge <?php echo $module_data['status'] === 'active' ? 'green' : ($module_data['status'] === 'internal_error' ? 'orange' : 'red'); ?>">
                            <?php echo wp_kses_post($status); ?>
                        </span>
                        <span style="margin-left:12px;vertical-align:middle;display:inline-block;">
                            <label style="font-weight:normal;cursor:pointer;">
                                <input type="checkbox" name="dstweaks_module[<?php echo esc_attr($module); ?>]" value="1" <?php echo $checked; ?> style="vertical-align:middle;"> <?php esc_html_e('Enabled', 'dstweaks'); ?>
                            </label>
                        </span>
                        <span class="icon"></span>
                    </button>
                    <div id="<?php echo $accordion_id; ?>" class="health-check-accordion-panel"<?php echo $is_open ? '' : ' hidden'; ?> >
                        <table class="health-check-table widefat striped">
                            <tbody>
                                <tr>
                                    <th style="width: 80%"><?php esc_html_e('Description:', 'dstweaks'); ?></th>
                                    <th><?php esc_html_e('Version:', 'dstweaks'); ?></th>
                                    <th style="min-width: 128px"><?php esc_html_e('Status:', 'dstweaks'); ?></th>
                                </tr>
                                <tr>
                                    <td><?php echo wp_kses_post($module_data['description']); ?></td>
                                    <td><?php echo wp_kses_post($module_data['version']); ?></td>
                                    <td><?php echo wp_kses_post($status); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3">
                                        <?php
                                        if ($is_user_admin) {
                                            $messages = [];
                                            if (!$is_active && !empty($module_data['errors'])) {
                                                $error_message = '';
                                                switch ($module_data['errors']) {
                                                    case 'file_missing':
                                                        $error_message = __('Module file not found.', 'dstweaks');
                                                        break;
                                                    case 'hash_mismatch':
                                                        $error_message = __('Module integrity check failed.', 'dstweaks');
                                                        break;
                                                    case 'invalid_path':
                                                        $error_message = __('Invalid module location.', 'dstweaks');
                                                        break;
                                                    case 'version_mismatch':
                                                        $error_message = __('Module version mismatch.', 'dstweaks');
                                                        break;
                                                    case 'manifest_file_missing':
                                                        $error_message = __('Module manifest file not found.', 'dstweaks');
                                                        break;
                                                    case 'manifest_hash_mismatch':
                                                        $error_message = __('Module manifest integrity check failed.', 'dstweaks');
                                                        break;
                                                }
                                                if ($error_message) {
                                                    $messages[] = '<span style="color:#b32d2e;font-weight:600;">Fatal error: ' . wp_kses_post($error_message) . '</span>';
                                                    $module_data['errors'] = '';
                                                }
                                            }
                                            if (!empty($module_data['messages'])) {
                                                $not_fatal = false;
                                                foreach ($module_data['messages'] as $message_entry) {
                                                    $type = $message_entry['type'];
                                                    $message = $message_entry['message'];
                                                    if ($message_entry['message'] == $module_data['errors'])
                                                        $not_fatal = true;
                                                    switch ($type) {
                                                        case 'good':
                                                            $icon = '<span class="dashicons dashicons-yes" style="color:#46b450;"></span>';
                                                            break;
                                                        case 'error':
                                                            $icon = '<span class="dashicons dashicons-no" style="color:#b32d2e;"></span>';
                                                            break;
                                                        case 'warning':
                                                        default:
                                                            $icon = '<span class="dashicons dashicons-warning" style="color:#dba617;"></span>';
                                                            break;
                                                    }
                                                    $messages[] = $icon . '<span style="font-weight:600;">' . wp_kses_post($message) . '</span>';
                                                }
                                            }
                                            if (!empty($module_data['errors']) && !$not_fatal) {
                                                $messages[] = '<span style="color:#b32d2e;font-weight:600;">Fatal error: ' . wp_kses_post($module_data['errors']) . '</span>';
                                            }
                                            echo $messages ? implode('<br>', $messages) : '<span>' . esc_html__('No more information.', 'dstweaks') . '</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php } ?>
                <div style="margin:16px 0;">
                    <button type="submit" name="dstweaks_save_modules" class="button button-primary"><?php esc_html_e('Save', 'dstweaks'); ?></button>
                    <button type="button" id="dstweaks-enable-all" class="button"><?php esc_html_e('Enable All', 'dstweaks'); ?></button>
                    <button type="button" id="dstweaks-disable-all" class="button"><?php esc_html_e('Disable All', 'dstweaks'); ?></button>
                </div>
            </div>
        </div>
    </form>

    <!-- Cache Management Section -->
    <div class="health-check-body" style="margin-top: 40px; padding: 20px; background: #fff; border: 1px solid #c3c4c7; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
        <h2><?php esc_html_e('Cache Management', 'dstweaks'); ?></h2>
        <p><?php esc_html_e('Use this section to manage the plugin cache. Clearing the cache will remove all stored transients and temporary data.', 'dstweaks'); ?></p>
        <form method="post" style="margin-top: 15px;">
            <?php wp_nonce_field('dstweaks_clear_cache', 'dstweaks_clear_cache_nonce'); ?>
            <button type="submit" name="dstweaks_clear_cache" class="button button-link-delete" style="padding: 4px 12px;">
                <span class="dashicons dashicons-trash" style="margin: 4px 8px 0 0;"></span>
                <?php esc_html_e('Clear All Caches', 'dstweaks'); ?>
            </button>
        </form>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Accordion logic: allow multiple open
        document.querySelectorAll('.health-check-accordion-trigger').forEach(function(btn) {
            btn.addEventListener('click', function() {
                var panel = document.getElementById(btn.getAttribute('aria-controls'));
                var isOpen = btn.getAttribute('aria-expanded') === 'true';
                if (!isOpen) {
                    btn.setAttribute('aria-expanded', 'true');
                    if(panel) panel.hidden = false;
                } else {
                    btn.setAttribute('aria-expanded', 'false');
                    if(panel) panel.hidden = true;
                }
            });
        });
        // Enable All/Disable All logic
        var enableAllBtn = document.getElementById('dstweaks-enable-all');
        var disableAllBtn = document.getElementById('dstweaks-disable-all');
        if (enableAllBtn && disableAllBtn) {
            enableAllBtn.addEventListener('click', function() {
                document.querySelectorAll('input[type="checkbox"][name^="dstweaks_module["]').forEach(function(cb) {
                    cb.checked = true;
                });
            });
            disableAllBtn.addEventListener('click', function() {
                document.querySelectorAll('input[type="checkbox"][name^="dstweaks_module["]').forEach(function(cb) {
                    cb.checked = false;
                });
            });
        }
    });
    </script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Accordion logic: allow multiple open
                document.querySelectorAll('.health-check-accordion-trigger').forEach(function(btn) {
                    btn.addEventListener('click', function() {
                        var panel = document.getElementById(btn.getAttribute('aria-controls'));
                        var isOpen = btn.getAttribute('aria-expanded') === 'true';
                        if (!isOpen) {
                            btn.setAttribute('aria-expanded', 'true');
                            if(panel) panel.hidden = false;
                        } else {
                            btn.setAttribute('aria-expanded', 'false');
                            if(panel) panel.hidden = true;
                        }
                    });
                });
            });
        </script>
    </div>
    <?php
    add_action('admin_footer', function () use ($dstweaks_plugin_info){
        $customHTML = '<p><i>' . wp_kses_post(sprintf(
            /* translators: 1: author URI */
            __('Thank you for using <a href="%1$s" target="_blank">DSTweaks</a>.', 'dstweaks'),
            $dstweaks_plugin_info['Author URI']
        )) . '</i><span style="float: right;">' . wp_kses_post(sprintf(
            /* translators: 1: plugin version */            
            __('Version %1$s', 'dstweaks'),
            $dstweaks_plugin_info['Version']
        )) . '</span></p>';
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const footer = document.getElementById('wpfooter');
                if (footer) {
                    const customHTML = '<?php echo $customHTML; ?>';
                    footer.insertAdjacentHTML('afterbegin', customHTML);
                }
            });
        </script>
        <?php
    });
    ?>
    <?php
}
