<?php
/**
 * Module Name: Dynamic Bar Counters
 * Description: Adds Dynamic Content support to the Bar Counters module's Percent field in Divi.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Divi_Module_DynamicBarCounters extends ET_Builder_Module_Bar_Counters {
    public $vb_support = 'on';

    function init() {
        parent::init();
        $this->name   = esc_html__( 'DST Dynamic Bar Counters', 'dstweaks' );
        $this->plural = esc_html__( 'DST Dynamic Bar Counters', 'dstweaks' );
    }

    function get_fields() {
        $fields = parent::get_fields();

        // Enable dynamic content for the Percent field shown in the UI
        if ( isset($fields['percent']) ) {
            $fields['percent']['dynamic_content'] = true;
        }

        return $fields;
    }
}
